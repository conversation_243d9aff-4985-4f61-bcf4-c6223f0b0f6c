const express = require('express');
const schoolSubscriptionController = require('../controllers/schoolSubscriptionController');
const { authenticate, authorize } = require('../middleware/middleware');

const router = express.Router();

// Routes pour les souscriptions d'école

// GET /school-subscription/:school_id - Obtenir la souscription d'une école
router.get('/:school_id',
  authenticate,
  authorize(['admin', 'super']),
  schoolSubscriptionController.getSchoolSubscription
);

// PUT /school-subscription/:school_id - Mettre à jour la souscription d'une école
router.put('/:school_id',
  authenticate,
  authorize(['admin', 'super']),
  schoolSubscriptionController.updateSchoolSubscription
);

// GET /school-subscription/:school_id/stats - Obtenir les statistiques de souscription
router.get('/:school_id/stats',
  authenticate,
  authorize(['admin', 'super']),
  schoolSubscriptionController.getSubscriptionStats
);

// GET /school-subscription/:school_id/credits/check - Vérifier la disponibilité des crédits
router.get('/:school_id/credits/check',
  authenticate,
  authorize(['admin', 'super', 'teacher']),
  schoolSubscriptionController.checkCreditsAvailability
);

// POST /school-subscription/:school_id/credits/deduct - Déduire des crédits
router.post('/:school_id/credits/deduct',
  authenticate,
  authorize(['admin', 'super', 'teacher']),
  schoolSubscriptionController.deductCredits
);

// GET /school-subscription/:school_id/usage - Obtenir l'historique d'utilisation
router.get('/:school_id/usage',
  authenticate,
  authorize(['admin', 'super']),
  schoolSubscriptionController.getCreditUsageHistory
);

// GET /school-subscription/plans/available - Obtenir les plans disponibles
router.get('/plans/available',
  authenticate,
  authorize(['admin', 'super']),
  schoolSubscriptionController.getAvailablePlans
);

// GET /school-subscription/:school_id/analytics/usage - Obtenir les données d'utilisation pour les graphiques
router.get('/:school_id/analytics/usage',
  authenticate,
  authorize(['admin', 'super', 'school_admin']),
  schoolSubscriptionController.getCreditUsageAnalytics
);

// GET /school-subscription/analytics/distribution - Obtenir la distribution des souscriptions (globale)
router.get('/analytics/distribution',
  authenticate,
  authorize(['admin', 'super']),
  schoolSubscriptionController.getSubscriptionDistribution
);

// GET /school-subscription/:school_id/analytics/distribution - Obtenir la distribution pour une école spécifique
router.get('/:school_id/analytics/distribution',
  authenticate,
  authorize(['admin', 'super', 'school_admin']),
  schoolSubscriptionController.getSubscriptionDistribution
);

// GET /school-subscription/:school_id/analytics/complete - Obtenir les analyses complètes d'une école
router.get('/:school_id/analytics/complete',
  authenticate,
  authorize(['admin', 'super']),
  schoolSubscriptionController.getSchoolCompleteAnalytics
);

module.exports = router;
