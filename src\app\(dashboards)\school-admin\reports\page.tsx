"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  CreditCard, 
  Calendar,
  Download,
  Filter,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  DollarSign,
  Target
} from 'lucide-react';
import SchoolLayout from '@/components/Dashboard/Layouts/SchoolLayout';
import useAuth from '@/app/hooks/useAuth';
import UsageChart from '@/components/analytics/UsageChart';
import SubscriptionOverview from '@/components/analytics/SubscriptionOverview';
import { 
  getCreditUsageAnalytics, 
  getSubscriptionDistribution,
  formatCurrency,
  formatCredits,
  UsageAnalyticsResponse,
  SubscriptionDistributionResponse
} from '@/app/services/SubscriptionServices';
import { getRegisteredStudentsStats, RegisteredStudentsStatsSchema } from '@/app/services/StudentServices';
import CircularLoader from '@/components/widgets/CircularLoader';
import { useAcademicYearContext } from '@/context/AcademicYearContext';

export default function ReportsPage() {
  const { user, logout } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  
  // États pour les données
  const [usageAnalytics, setUsageAnalytics] = useState<UsageAnalyticsResponse | null>(null);
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionDistributionResponse | null>(null);
  const [studentStats, setStudentStats] = useState<RegisteredStudentsStatsSchema | null>(null);
  
  // États pour les filtres
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'quarter' | 'year'>('month');
  const [dateRange, setDateRange] = useState({
    start: new Date(new Date().setDate(new Date().getDate() - 30)).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  });
  const { currentAcademicYear } = useAcademicYearContext();

  const breadcrumb = {
    icon: BarChart3,
    baseHref: `/school-admin/reports`,
    title: "Rapports et Analytics"
  };

  useEffect(() => {
    if (user?.school_ids?.[0]) {
      fetchAllData();
    }
  }, [user, selectedPeriod]);

  const fetchAllData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const schoolId = user?.school_ids?.[0];
      if (!schoolId) {
        throw new Error('Aucune école associée');
      }

      // Récupérer toutes les données en parallèle
      const [usageResponse, distributionResponse, studentResponse] = await Promise.allSettled([
        getCreditUsageAnalytics(schoolId, selectedPeriod),
        getSubscriptionDistribution(),
        getRegisteredStudentsStats(schoolId, currentAcademicYear)
      ]);

      // Traiter les résultats
      if (usageResponse.status === 'fulfilled') {
        setUsageAnalytics(usageResponse.value);
      } else {
        console.error('Error fetching usage analytics:', usageResponse.reason);
      }

      if (distributionResponse.status === 'fulfilled') {
        setSubscriptionData(distributionResponse.value);
      } else {
        console.error('Error fetching subscription distribution:', distributionResponse.reason);
      }

      if (studentResponse.status === 'fulfilled') {
        setStudentStats(studentResponse.value);
      } else {
        console.error('Error fetching student stats:', studentResponse.reason);
      }

    } catch (err: any) {
      setError(err.message || 'Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchAllData();
    setRefreshing(false);
  };

  const exportReport = () => {
    // TODO: Implémenter l'export des rapports
    console.log('Export report functionality to be implemented');
  };

  // Calculs des métriques clés
  const calculateMetrics = () => {
    if (!usageAnalytics || !studentStats) return null;

    const { summary } = usageAnalytics;
    const efficiency = summary.total_purchased > 0 
      ? (summary.total_used / summary.total_purchased) * 100 
      : 0;

    const burnRate = summary.current_balance > 0 && summary.total_used > 0
      ? summary.total_used / 30 // Utilisation par jour (approximation)
      : 0;

    const daysRemaining = burnRate > 0 
      ? Math.ceil(summary.current_balance / burnRate)
      : 0;

    return {
      efficiency: Math.round(efficiency),
      burnRate: Math.round(burnRate * 10) / 10,
      daysRemaining,
      arpu: studentStats.registered_students > 0 
        ? Math.round(studentStats.total_revenue / studentStats.registered_students)
        : 0
    };
  };

  const metrics = calculateMetrics();

  if (loading) {
    return (
      <SchoolLayout navigation={breadcrumb}
        showGoPro={true}
        onLogout={() => logout()}
      >
        <div className="flex items-center justify-center min-h-screen">
          <CircularLoader />
        </div>
      </SchoolLayout>
    );
  }

  return (
    <SchoolLayout navigation={breadcrumb}
        showGoPro={true}
        onLogout={() => logout()}
      >
      <div className="p-6 max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Rapports et Analytics
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Analysez les performances et l'utilisation de votre école
            </p>
          </div>

          <div className="flex items-center space-x-4">
            {/* Period Filter */}
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-gray-400" />
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value as any)}
                className="text-sm border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
              >
                <option value="week">Cette semaine</option>
                <option value="month">Ce mois</option>
                <option value="quarter">Ce trimestre</option>
                <option value="year">Cette année</option>
              </select>
            </div>

            {/* Actions */}
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors disabled:opacity-50"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Actualiser
            </button>

            <button
              onClick={exportReport}
              className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              <Download className="h-4 w-4 mr-2" />
              Exporter
            </button>
          </div>
        </div>

        {/* Error State */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
              <p className="text-red-700 dark:text-red-300">{error}</p>
            </div>
          </div>
        )}

        {/* Key Metrics Cards */}
        {metrics && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 border-l-4 border-blue-500"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Efficacité</p>
                  <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">
                    {metrics.efficiency}%
                  </p>
                </div>
                <Target className="h-8 w-8 text-blue-500" />
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                Crédits utilisés vs achetés
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 border-l-4 border-green-500"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">ARPU</p>
                  <p className="text-3xl font-bold text-green-600 dark:text-green-400">
                    {formatCurrency(metrics.arpu)}
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-green-500" />
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                Revenu moyen par étudiant
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 border-l-4 border-orange-500"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Taux d'utilisation</p>
                  <p className="text-3xl font-bold text-orange-600 dark:text-orange-400">
                    {metrics.burnRate}/jour
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-orange-500" />
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                Crédits utilisés quotidiennement
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 border-l-4 border-purple-500"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Autonomie</p>
                  <p className="text-3xl font-bold text-purple-600 dark:text-purple-400">
                    {metrics.daysRemaining}j
                  </p>
                </div>
                <Calendar className="h-8 w-8 text-purple-500" />
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                Jours restants estimés
              </p>
            </motion.div>
          </div>
        )}

        {/* Charts Section */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
          {/* Usage Chart */}
          <UsageChart 
            schoolId={user?.school_ids?.[0] || ''} 
            period={selectedPeriod}
            className="xl:col-span-2"
          />

          {/* Subscription Overview */}
          <SubscriptionOverview 
            schoolId={user?.school_ids?.[0] || ''} 
            className="xl:col-span-2"
          />
        </div>

        {/* Student Statistics */}
        {studentStats && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <Users className="h-5 w-5 mr-2 text-blue-600" />
              Statistiques des Étudiants
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">
                  {studentStats.registered_students}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Étudiants enregistrés</p>
              </div>
              
              <div className="text-center">
                <p className="text-3xl font-bold text-green-600 dark:text-green-400">
                  {studentStats.registration_rate}%
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Taux d'enregistrement</p>
              </div>
              
              <div className="text-center">
                <p className="text-3xl font-bold text-purple-600 dark:text-purple-400">
                  {formatCurrency(studentStats.total_revenue)}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Revenus totaux</p>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </SchoolLayout>
  );
}
