"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  CreditCard, 
  Calendar,
  Download,
  Filter,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  DollarSign,
  Target,
  Building2,
  Globe
} from 'lucide-react';
import SuperLayout from "@/components/Dashboard/Layouts/SuperLayout";
import useAuth from '@/app/hooks/useAuth';
import GlobalSubscriptionOverview from '@/components/analytics/GlobalSubscriptionOverview';
import { 
  getCreditUsageAnalytics, 
  getSubscriptionDistribution,
  formatCurrency,
  formatCredits,
  UsageAnalyticsResponse,
  SubscriptionDistributionResponse
} from '@/app/services/SubscriptionServices';
import CircularLoader from '@/components/widgets/CircularLoader';

export default function SuperAdminReportsPage() {
  const { user, logout } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  
  // États pour les données globales
  const [globalSubscriptionData, setGlobalSubscriptionData] = useState<SubscriptionDistributionResponse | null>(null);
  
  // États pour les filtres
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'quarter' | 'year'>('month');

  const breadcrumb = {
    icon: BarChart3,
    baseHref: `/super-admin/reports`,
    title: "Rapports Globaux et Analytics"
  };

  useEffect(() => {
    fetchAllData();
  }, [selectedPeriod]);

  const fetchAllData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Récupérer les données globales (sans schoolId)
      const [distributionResponse] = await Promise.allSettled([
        getSubscriptionDistribution() // Pas de schoolId = données globales
      ]);

      // Traiter les résultats
      if (distributionResponse.status === 'fulfilled') {
        setGlobalSubscriptionData(distributionResponse.value);
      } else {
        console.error('Error fetching global subscription distribution:', distributionResponse.reason);
      }

    } catch (err: any) {
      setError(err.message || 'Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchAllData();
    setRefreshing(false);
  };

  const exportReport = () => {
    // TODO: Implémenter l'export des rapports globaux
    console.log('Export global report functionality to be implemented');
  };

  // Calculs des métriques globales
  const calculateGlobalMetrics = () => {
    if (!globalSubscriptionData) return null;

    const { summary } = globalSubscriptionData;
    
    return {
      totalSchools: summary.total_subscriptions,
      totalRevenue: summary.total_revenue,
      mostPopularPlan: summary.most_popular_plan,
      averageRevenuePerSchool: summary.total_subscriptions > 0 
        ? Math.round(summary.total_revenue / summary.total_subscriptions)
        : 0
    };
  };

  const metrics = calculateGlobalMetrics();

  if (loading) {
    return (
      <SuperLayout navigation={breadcrumb}
        showGoPro={true}
        onLogout={() => logout()}
      >
        <div className="flex items-center justify-center min-h-screen">
          <CircularLoader />
        </div>
      </SuperLayout>
    );
  }

  return (
    <SuperLayout navigation={breadcrumb}
        showGoPro={true}
        onLogout={() => logout()}
      >
      <div className="p-6 max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Rapports Globaux et Analytics
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Vue d'ensemble des performances de toutes les écoles
            </p>
          </div>

          <div className="flex items-center space-x-4">
            {/* Period Filter */}
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-gray-400" />
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value as any)}
                className="text-sm border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
              >
                <option value="week">Cette semaine</option>
                <option value="month">Ce mois</option>
                <option value="quarter">Ce trimestre</option>
                <option value="year">Cette année</option>
              </select>
            </div>

            {/* Actions */}
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors disabled:opacity-50"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Actualiser
            </button>

            <button
              onClick={exportReport}
              className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              <Download className="h-4 w-4 mr-2" />
              Exporter
            </button>
          </div>
        </div>

        {/* Error State */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
              <p className="text-red-700 dark:text-red-300">{error}</p>
            </div>
          </div>
        )}

        {/* Global Metrics Cards */}
        {metrics && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 border-l-4 border-blue-500"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Écoles</p>
                  <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">
                    {metrics.totalSchools}
                  </p>
                </div>
                <Building2 className="h-8 w-8 text-blue-500" />
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                Écoles actives sur la plateforme
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 border-l-4 border-green-500"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Revenus Totaux</p>
                  <p className="text-3xl font-bold text-green-600 dark:text-green-400">
                    {formatCurrency(metrics.totalRevenue)}
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-green-500" />
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                Revenus de toutes les écoles
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 border-l-4 border-orange-500"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Plan Populaire</p>
                  <p className="text-3xl font-bold text-orange-600 dark:text-orange-400">
                    {metrics.mostPopularPlan}
                  </p>
                </div>
                <Target className="h-8 w-8 text-orange-500" />
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                Plan le plus souscrit
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 border-l-4 border-purple-500"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Revenu/École</p>
                  <p className="text-3xl font-bold text-purple-600 dark:text-purple-400">
                    {formatCurrency(metrics.averageRevenuePerSchool)}
                  </p>
                </div>
                <Globe className="h-8 w-8 text-purple-500" />
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                Revenu moyen par école
              </p>
            </motion.div>
          </div>
        )}

        {/* Global Subscription Overview */}
        <GlobalSubscriptionOverview className="w-full" />
      </div>
    </SuperLayout>
  );
}
