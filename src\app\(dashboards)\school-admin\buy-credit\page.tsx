"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  CreditCard,
  TrendingUp,
  AlertTriangle,
  Plus,
  Eye,
  Calendar,
  Users,
  MessageCircle,
  BarChart3,
  ExternalLink,
  Zap
} from 'lucide-react';
import useAuth from '@/app/hooks/useAuth';
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import {
  getSubscriptionStats,
  formatCurrency,
  formatCredits
} from '@/app/services/SubscriptionServices';
import RecentTransactions from '@/components/credit/RecentTransactions';
import { SchoolSubscriptionSchema } from '@/app/models/SchoolSubscriptionModel';
import { getRegisteredStudentsStats, RegisteredStudentsStatsSchema } from '@/app/services/StudentServices';
import UsageChart from '@/components/analytics/UsageChart';
import { useSubscriptionCredits } from '@/context/CreditContext';
import CircularLoader from '@/components/widgets/CircularLoader';
import CreditPurchaseModal from '@/components/modals/CreditPurchaseModal';
import { DebugCreditService } from '@/utils/debugCreditService';
// import SubscriptionOverview from '@/components/school-admin/SubscriptionOverview';
// import UsageChart from '@/components/school-admin/UsageChart';
// import RecentTransactions from '@/components/school-admin/RecentTransactions';
import Link from 'next/link';
import { useAcademicYearContext } from '@/context/AcademicYearContext';

const BASE_URL = "/school-admin";

export default function BuyCreditPage() {
  const { user, logout } = useAuth();
  const {
    availableCredits,
    totalPaid,
    creditCount,
    subscription,
    loading: creditsLoading,
    refreshAll,
    error: creditsError,
    isLowBalance,
    pendingPurchases,
    hasPendingPurchases
  } = useSubscriptionCredits();
  const [studentStats, setStudentStats] = useState<RegisteredStudentsStatsSchema | null>(null);
  const [studentStatsLoading, setStudentStatsLoading] = useState(true);
  const [studentStatsError, setStudentStatsError] = useState<string | null>(null);
  const [showPurchaseModal, setShowPurchaseModal] = useState(false);
  const { currentAcademicYear } = useAcademicYearContext();
  const schoolId: any = user?.school_ids?.[0] || user?.school_id;

  console.log(" subscription credits data ",{
    availableCredits,
    totalPaid,
    creditCount,
    subscription,
    loading: creditsLoading,
    refreshAll,
    error: creditsError,
    isLowBalance,
    pendingPurchases,
    hasPendingPurchases
  })
  // Navigation avec titre dynamique
  const navigation = {
    icon: CreditCard,
    baseHref: `${BASE_URL}/buy-credit`,
    title: "Gestion des Crédits"
  };

  useEffect(() => {
    if (user?.school_ids && user.school_ids.length > 0) {
      fetchStudentStats();
    }
  }, [user]);

  const fetchStudentStats = async () => {
    try {
      setStudentStatsLoading(true);
      setStudentStatsError(null);

      if (!user?.school_ids || user.school_ids.length === 0) {
        throw new Error('Aucune école associée à cet utilisateur');
      }

      const statsResponse = await getRegisteredStudentsStats(schoolId, currentAcademicYear);
      setStudentStats(statsResponse);
    } catch (err: any) {
      setStudentStatsError(err.message || 'Erreur lors du chargement des statistiques des étudiants');
      console.error('Error fetching student stats:', err);
    } finally {
      setStudentStatsLoading(false);
    }
  };

  const handlePurchaseSuccess = () => {
    setShowPurchaseModal(false);
    refreshAll(); // Refresh credits after purchase
    fetchStudentStats(); // Refresh student stats
  };

  const loading = creditsLoading || studentStatsLoading;
  const error = creditsError || studentStatsError;

  if (loading) {
    return (
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        <div className="flex items-center justify-center min-h-96">
          <CircularLoader size={48} color="teal" />
        </div>
      </SchoolLayout>
    );
  }

  if (error || !subscription) {
    return (
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">Erreur</h2>
            <p className="text-gray-600 dark:text-gray-400 mb-4">{error || 'Données de souscription non trouvées'}</p>
            <button
              onClick={() => {
                refreshAll();
                fetchStudentStats();
              }}
              className="px-6 py-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white rounded-lg transition-colors"
            >
              Réessayer
            </button>
          </div>
        </div>
      </SchoolLayout>
    );
  }

  const efficiencyScore = subscription?.credits_purchased && subscription.credits_purchased > 0
    ? Math.round(((subscription.credits_used || 0) / subscription.credits_purchased) * 100)
    : 0;

  return (
    <SchoolLayout
      navigation={navigation}
      showGoPro={true}
      onLogout={() => logout()}
    >
      <div className="p-6">
        <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                Gestion des Crédits
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Gérez votre souscription et achetez des crédits pour votre école
              </p>
            </div>
            
            <div className="flex gap-3">
              <button
                onClick={() => {
                  if (user?.school_ids?.[0]) {
                    DebugCreditService.runFullTest(user.school_ids[0]);
                  }
                }}
                className="px-4 py-2 border border-yellow-300 dark:border-yellow-600 text-yellow-700 dark:text-yellow-300 rounded-lg hover:bg-yellow-50 dark:hover:bg-yellow-700 flex items-center transition-colors"
              >
                🧪 Debug
              </button>
              <Link
                href="/pricing"
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center transition-colors"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Voir les plans
              </Link>
              <button
                onClick={() => setShowPurchaseModal(true)}
                className="px-6 py-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white rounded-lg flex items-center transition-colors"
              >
                <Plus className="h-4 w-4 mr-2" />
                Acheter des crédits
              </button>
            </div>
          </div>
        </div>

        {/* Alert for Low Balance */}
        {isLowBalance && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6 p-4 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg"
          >
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-orange-500 mr-3" />
              <div>
                <h3 className="font-medium text-orange-800 dark:text-orange-200">Solde de crédits faible</h3>
                <p className="text-orange-700 dark:text-orange-300 text-sm">
                  Il vous reste seulement {formatCredits(availableCredits)}.
                  Pensez à recharger pour continuer à utiliser toutes les fonctionnalités.
                </p>
              </div>
              <button
                onClick={() => setShowPurchaseModal(true)}
                className="ml-auto px-4 py-2 bg-orange-600 hover:bg-orange-700 dark:bg-orange-500 dark:hover:bg-orange-600 text-white rounded-lg text-sm transition-colors"
              >
                Recharger maintenant
              </button>
            </div>
          </motion.div>
        )}

        {/* Alert for Pending Purchases */}
        {hasPendingPurchases && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg"
          >
            <div className="flex items-center">
              <Zap className="h-5 w-5 text-blue-500 mr-3" />
              <div className="flex-1">
                <h3 className="font-medium text-blue-800 dark:text-blue-200">Paiement en attente</h3>
                <p className="text-blue-700 dark:text-blue-300 text-sm">
                  Vous avez {pendingPurchases.length} paiement(s) en attente.
                  Cliquez pour finaliser votre achat de crédits.
                </p>
              </div>
              <div className="flex gap-2">
                {pendingPurchases.slice(0, 2).map((purchase) => (
                  <a
                    key={purchase._id}
                    href={purchase.payment_gateway_response?.link || '#'}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white rounded-lg text-sm transition-colors flex items-center"
                  >
                    <ExternalLink className="h-4 w-4 mr-1" />
                    Finaliser ({formatCredits(purchase.credits_purchased)})
                  </a>
                ))}
              </div>
            </div>
          </motion.div>
        )}

        {/* Main Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Credits Balance */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 border-l-4 border-blue-500"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Crédits disponibles</p>
                <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">
                  {availableCredits.toLocaleString()}
                </p>
              </div>
              <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                <CreditCard className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <span className={`px-2 py-1 rounded-full text-xs ${
                isLowBalance
                  ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
                  : 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
              }`}>
                {isLowBalance ? 'Solde faible' : 'Solde normal'}
              </span>
            </div>
          </motion.div>

          {/* Credits Used */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 border-l-4 border-green-500"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Crédits utilisés</p>
                <p className="text-3xl font-bold text-green-600 dark:text-green-400">
                  {subscription?.credits_used?.toLocaleString() || 0}
                </p>
              </div>
              <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-full">
                <TrendingUp className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm text-gray-600 dark:text-gray-400">
              <span>Efficacité: {efficiencyScore}%</span>
            </div>
          </motion.div>

          {/* Total Paid */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 border-l-4 border-purple-500"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total payé</p>
                <p className="text-3xl font-bold text-purple-600 dark:text-purple-400">
                  {formatCurrency(totalPaid)}
                </p>
              </div>
              <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-full">
                <BarChart3 className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm text-gray-600 dark:text-gray-400">
              <span>Équivalent: {availableCredits} crédits</span>
            </div>
          </motion.div>

          {/* Students Count */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 border-l-4 border-orange-500"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Étudiants enregistrés</p>
                <p className="text-3xl font-bold text-orange-600 dark:text-orange-400">
                  {studentStatsLoading ? (
                    <span className="animate-pulse">...</span>
                  ) : studentStatsError ? (
                    <span className="text-red-500 text-lg">Erreur</span>
                  ) : (
                    studentStats?.registered_students || 0
                  )}
                </p>
              </div>
              <div className="p-3 bg-orange-100 dark:bg-orange-900/30 rounded-full">
                <Users className="h-6 w-6 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm text-gray-600 dark:text-gray-400">
              {studentStats && !studentStatsLoading && !studentStatsError ? (
                <>
                  <span className="text-blue-600 dark:text-blue-400 font-medium">
                    {studentStats.registration_rate}% du total
                  </span>
                  <span className="ml-2">
                    ({studentStats.total_students} étudiants au total)
                  </span>
                </>
              ) : (
                <span>Paiements enregistrés</span>
              )}
            </div>
          </motion.div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Subscription Overview & Usage Chart */}
          <div className="lg:col-span-2 space-y-8">
            {/* <SubscriptionOverview subscription={subscription} stats={stats} /> */}
            {/* <UsageChart stats={stats} /> */}

            {/* Temporary placeholder content */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Aperçu de la souscription</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Plan actuel</p>
                  <p className="text-lg font-semibold capitalize dark:text-gray-200">{subscription.plan_type}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Statut</p>
                  <p className="text-lg font-semibold capitalize dark:text-gray-200">{subscription.status}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Crédits achetés</p>
                  <p className="text-lg font-semibold dark:text-gray-200">{subscription.credits_purchased.toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Fonctionnalités</p>
                  <p className="text-sm text-gray-700 dark:text-gray-300">{subscription.features.length} activées</p>
                </div>
              </div>
            </div>

            {/* Usage Chart */}
            <UsageChart
              schoolId={user?.school_ids?.[0] || ''}
              period="month"
            />
          </div>

          {/* Right Column - Recent Transactions & Quick Actions */}
          <div className="space-y-8">
            <RecentTransactions
              schoolId={user?.school_ids?.[0] || ''}
              onPurchaseClick={() => setShowPurchaseModal(true)}
            />
            
            {/* Quick Actions */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Actions rapides</h3>
              <div className="space-y-3">
                <button
                  onClick={() => setShowPurchaseModal(true)}
                  className="w-full p-3 text-left border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center transition-colors"
                >
                  <Plus className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-3" />
                  <div>
                    <div className="font-medium text-gray-900 dark:text-gray-100">Acheter des crédits</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Recharger votre solde</div>
                  </div>
                </button>
                
                <Link
                  href="/school-admin/buy-credit/history"
                  className="w-full p-3 text-left border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center transition-colors"
                >
                  <Eye className="h-5 w-5 text-green-600 dark:text-green-400 mr-3" />
                  <div>
                    <div className="font-medium text-gray-900 dark:text-gray-100">Historique détaillé</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Voir toutes les transactions</div>
                  </div>
                </Link>

                <Link
                  href="/pricing"
                  className="w-full p-3 text-left border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center transition-colors"
                >
                  <ExternalLink className="h-5 w-5 text-purple-600 dark:text-purple-400 mr-3" />
                  <div>
                    <div className="font-medium text-gray-900 dark:text-gray-100">Changer de plan</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Découvrir nos offres</div>
                  </div>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Purchase Modal */}
      {showPurchaseModal && (
        <CreditPurchaseModal
          isOpen={showPurchaseModal}
          onClose={() => setShowPurchaseModal(false)}
          onSuccess={handlePurchaseSuccess}
          schoolId={user?.school_ids?.[0] || ''}
          currentPlan={subscription?.plan_type || 'basic'}
        />
      )}
    </div>
    </SchoolLayout>
  );
}
