import { BASE_API_URL } from "./AuthContext";
import { getTokenFromCookie } from "./UserServices";

// Interfaces pour CreditPurchase
export interface CreditPurchaseSchema {
  _id: string;
  school_id: string;
  subscription_id: string;
  transaction_id: string;
  purchase_id: string;
  credits_purchased: number;
  price_per_credit: number;
  total_amount: number;
  currency: string;
  payment_method: string;
  payment_status: 'pending' | 'completed' | 'failed' | 'refunded';
  payment_gateway_response: Record<string, any>;
  purchase_date: string;
  payment_completed_date?: string;
  purchased_by: string;
  purchaser_email: string;
  billing_info?: {
    name?: string;
    email?: string;
    phone?: string;
    address?: string;
    organization?: string;
  };
  promotion_code?: string;
  discount_amount: number;
  discount_percentage: number;
  notes?: string;
  admin_notes?: string;
  refund_info?: {
    refund_date?: string;
    refund_amount?: number;
    refund_reason?: string;
    refunded_by?: string;
  };
  is_processed: boolean;
  processed_date?: string;
  processed_by?: string;
  createdAt?: string;
  updatedAt?: string;
}

// Interface pour les analyses complètes d'une école
export interface SchoolCompleteAnalyticsResponse {
  school: {
    _id: string;
    name: string;
    address: string;
    email: string;
    phone: string;
  };
  subscription: {
    plan_type: string;
    status: string;
    credits_balance: number;
    credits_purchased: number;
    credits_used: number;
    subscription_start: string;
    subscription_end?: string;
    features: string[];
  };
  analytics: {
    total_purchased: number;
    total_used: number;
    total_revenue: number;
    current_balance: number;
    efficiency: number;
    arpu: number;
    average_purchase: number;
    days_remaining: number;
    purchase_count: number;
  };
  recent_purchases: {
    purchase_id: string;
    credits_purchased: number;
    total_amount: number;
    purchase_date: string;
    payment_method: string;
  }[];
}

// Headers d'authentification
const getAuthHeaders = () => {
  const token = getTokenFromCookie("idToken");
  return {
    "Content-Type": "application/json",
    Authorization: `Bearer ${token}`,
  };
};

// Récupérer l'historique des achats pour une école
export async function getCreditPurchaseHistory(
  schoolId: string,
  limit: number = 50,
  skip: number = 0
): Promise<CreditPurchaseSchema[]> {
  const response = await fetch(
    `${BASE_API_URL}/credit-purchase/school/${schoolId}/history?limit=${limit}&skip=${skip}`,
    {
      method: 'GET',
      headers: getAuthHeaders(),
    }
  );

  if (!response.ok) {
    throw new Error('Failed to fetch credit purchase history');
  }

  return response.json();
}

// Récupérer les détails d'un achat spécifique
export async function getCreditPurchaseDetails(purchaseId: string): Promise<CreditPurchaseSchema> {
  const response = await fetch(
    `${BASE_API_URL}/credit-purchase/details/${purchaseId}`,
    {
      method: 'GET',
      headers: getAuthHeaders(),
    }
  );

  if (!response.ok) {
    throw new Error('Failed to fetch credit purchase details');
  }

  return response.json();
}

// Récupérer les analyses complètes d'une école
export async function getSchoolCompleteAnalytics(schoolId: string): Promise<SchoolCompleteAnalyticsResponse> {
  const response = await fetch(
    `${BASE_API_URL}/school-subscription/${schoolId}/analytics/complete`,
    {
      method: 'GET',
      headers: getAuthHeaders(),
    }
  );

  if (!response.ok) {
    throw new Error('Failed to fetch school complete analytics');
  }

  return response.json();
}

// Initier un achat de crédits
export async function initiateCreditPurchase(data: {
  school_id: string;
  credits_amount: number;
  payment_method?: string;
  billing_info?: Record<string, any>;
  promotion_code?: string;
  redirect_url?: string;
}): Promise<any> {
  const response = await fetch(
    `${BASE_API_URL}/credit-purchase/initiate`,
    {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(data),
    }
  );

  if (!response.ok) {
    throw new Error('Failed to initiate credit purchase');
  }

  return response.json();
}

// Vérifier le statut d'un paiement
export async function checkPaymentStatus(transactionId: string): Promise<any> {
  const response = await fetch(
    `${BASE_API_URL}/credit-purchase/status/${transactionId}`,
    {
      method: 'GET',
      headers: getAuthHeaders(),
    }
  );

  if (!response.ok) {
    throw new Error('Failed to check payment status');
  }

  return response.json();
}

// Fonctions utilitaires
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'XAF',
    minimumFractionDigits: 0,
  }).format(amount);
};

export const formatCredits = (credits: number): string => {
  return `${credits.toLocaleString()} crédit${credits > 1 ? 's' : ''}`;
};

export const getPaymentStatusColor = (status: string): string => {
  switch (status) {
    case 'completed':
      return 'text-green-600 bg-green-100';
    case 'pending':
      return 'text-yellow-600 bg-yellow-100';
    case 'failed':
      return 'text-red-600 bg-red-100';
    case 'refunded':
      return 'text-gray-600 bg-gray-100';
    default:
      return 'text-gray-600 bg-gray-100';
  }
};

export const getPaymentStatusText = (status: string): string => {
  switch (status) {
    case 'completed':
      return 'Complété';
    case 'pending':
      return 'En attente';
    case 'failed':
      return 'Échoué';
    case 'refunded':
      return 'Remboursé';
    default:
      return 'Inconnu';
  }
};

export const getPlanTypeText = (planType: string): string => {
  switch (planType) {
    case 'basic':
      return 'Basic';
    case 'standard':
      return 'Standard';
    case 'custom':
      return 'Custom';
    default:
      return planType;
  }
};

export const getPlanTypeColor = (planType: string): string => {
  switch (planType) {
    case 'basic':
      return 'text-blue-600 bg-blue-100';
    case 'standard':
      return 'text-green-600 bg-green-100';
    case 'custom':
      return 'text-purple-600 bg-purple-100';
    default:
      return 'text-gray-600 bg-gray-100';
  }
};
