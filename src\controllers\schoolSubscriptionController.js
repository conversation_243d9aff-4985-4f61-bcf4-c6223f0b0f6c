const SchoolSubscription = require('../models/SchoolSubscription');
const SubscriptionPlan = require('../models/SubscriptionPlan');
const CreditPurchase = require('../models/CreditPurchase');
const CreditUsage = require('../models/CreditUsage');
const School = require('../models/School');
const mongoose = require('mongoose');
const { ObjectId } = mongoose.Types;

// Obtenir la souscription d'une école
const getSchoolSubscription = async (req, res) => {
  try {
    const { school_id } = req.params;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    let subscription = await SchoolSubscription.findOne({ school_id })
      .populate('credit_purchases')
      .populate('school_id', 'name email');

    // Créer une souscription par défaut si elle n'existe pas
    if (!subscription) {
      subscription = await SchoolSubscription.createDefaultSubscription(school_id);
      await subscription.populate('school_id', 'name email');
    }

    // Réinitialiser l'usage mensuel du chatbot si nécessaire
    await subscription.resetMonthlyChatbotUsage();

    res.status(200).json({
      subscription,
      message: 'School subscription retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching school subscription:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Mettre à jour la souscription d'une école
const updateSchoolSubscription = async (req, res) => {
  try {
    const { school_id } = req.params;
    const {
      plan_type,
      low_credit_threshold,
      notifications_enabled,
      admin_notes
    } = req.body;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    const subscription = await SchoolSubscription.findOne({ school_id });
    if (!subscription) {
      return res.status(404).json({ message: 'School subscription not found' });
    }

    // Mettre à jour les champs modifiables
    if (plan_type && ['basic', 'standard', 'custom'].includes(plan_type)) {
      subscription.plan_type = plan_type;
    }
    
    if (typeof low_credit_threshold === 'number') {
      subscription.low_credit_threshold = Math.max(0, low_credit_threshold);
    }
    
    if (typeof notifications_enabled === 'boolean') {
      subscription.notifications_enabled = notifications_enabled;
    }
    
    if (admin_notes !== undefined) {
      subscription.admin_notes = admin_notes;
    }

    await subscription.save();

    res.status(200).json({
      subscription,
      message: 'School subscription updated successfully'
    });
  } catch (error) {
    console.error('Error updating school subscription:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Obtenir les statistiques de la souscription
const getSubscriptionStats = async (req, res) => {
  try {
    const { school_id } = req.params;
    const { period = 'month' } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    const subscription = await SchoolSubscription.findOne({ school_id });
    if (!subscription) {
      return res.status(404).json({ message: 'School subscription not found' });
    }

    // Statistiques d'utilisation
    const usageStats = await CreditUsage.getUsageStats(school_id, period);
    
    // Historique des achats récents
    const recentPurchases = await CreditPurchase.getBySchoolId(school_id, 10);
    
    // Utilisation quotidienne
    const dailyUsage = await CreditUsage.getDailyUsage(school_id, 30);

    // Calculs supplémentaires
    const totalPurchased = await CreditPurchase.getTotalPurchasedCredits(school_id);
    const efficiency = {
      credits_per_day: dailyUsage.length > 0 ? 
        dailyUsage.reduce((sum, day) => sum + day.total_credits, 0) / dailyUsage.length : 0,
      burn_rate: subscription.credits_used > 0 && subscription.credits_purchased > 0 ?
        (subscription.credits_used / subscription.credits_purchased) * 100 : 0,
      days_remaining: subscription.credits_balance > 0 && dailyUsage.length > 0 ?
        subscription.credits_balance / (dailyUsage.reduce((sum, day) => sum + day.total_credits, 0) / dailyUsage.length) : 0
    };

    res.status(200).json({
      subscription_overview: {
        plan_type: subscription.plan_type,
        status: subscription.status,
        credits_balance: subscription.credits_balance,
        credits_purchased: subscription.credits_purchased,
        credits_used: subscription.credits_used,
        last_purchase: subscription.last_credit_purchase,
        last_usage: subscription.last_credit_usage
      },
      usage_stats: usageStats,
      recent_purchases: recentPurchases,
      daily_usage: dailyUsage,
      efficiency_metrics: efficiency,
      total_purchased: totalPurchased[0] || { total_credits: 0, total_amount: 0 },
      message: 'Subscription statistics retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching subscription stats:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Vérifier si l'école a suffisamment de crédits
const checkCreditsAvailability = async (req, res) => {
  try {
    const { school_id } = req.params;
    const { credits_needed = 1 } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    const subscription = await SchoolSubscription.findOne({ school_id });
    if (!subscription) {
      return res.status(404).json({ message: 'School subscription not found' });
    }

    const hasCredits = subscription.hasCredits(parseInt(credits_needed));
    const isLowBalance = subscription.credits_balance <= subscription.low_credit_threshold;

    res.status(200).json({
      has_credits: hasCredits,
      credits_balance: subscription.credits_balance,
      credits_needed: parseInt(credits_needed),
      is_low_balance: isLowBalance,
      low_credit_threshold: subscription.low_credit_threshold,
      message: hasCredits ? 'Credits available' : 'Insufficient credits'
    });
  } catch (error) {
    console.error('Error checking credits availability:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Déduire des crédits (utilisé par d'autres services)
const deductCredits = async (req, res) => {
  try {
    const { school_id } = req.params;
    const {
      credits_amount = 1,
      usage_type,
      reference_id,
      reference_type,
      description,
      details = {},
      session_info = {}
    } = req.body;

    if (!school_id || !usage_type || !description) {
      return res.status(400).json({ 
        message: 'School ID, usage type, and description are required' 
      });
    }

    const subscription = await SchoolSubscription.findOne({ school_id });
    if (!subscription) {
      return res.status(404).json({ message: 'School subscription not found' });
    }

    if (!subscription.hasCredits(credits_amount)) {
      return res.status(400).json({ 
        message: 'Insufficient credits',
        credits_balance: subscription.credits_balance,
        credits_needed: credits_amount
      });
    }

    const balance_before = subscription.credits_balance;
    
    // Déduire les crédits
    await subscription.deductCredits(credits_amount, usage_type, reference_id, req.user.id);
    
    // Enregistrer l'utilisation
    await CreditUsage.recordUsage({
      school_id,
      subscription_id: subscription._id,
      usage_type,
      credits_used: credits_amount,
      reference_id,
      reference_type,
      used_by: req.user.id,
      description,
      details,
      balance_before,
      balance_after: subscription.credits_balance,
      session_info
    });

    res.status(200).json({
      success: true,
      credits_deducted: credits_amount,
      new_balance: subscription.credits_balance,
      message: 'Credits deducted successfully'
    });
  } catch (error) {
    console.error('Error deducting credits:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Obtenir l'historique d'utilisation des crédits
const getCreditUsageHistory = async (req, res) => {
  try {
    const { school_id } = req.params;
    const {
      limit = 50,
      skip = 0,
      usage_type,
      start_date,
      end_date,
      used_by
    } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    const options = {
      limit: parseInt(limit),
      skip: parseInt(skip),
      usage_type,
      start_date,
      end_date,
      used_by
    };

    const usageHistory = await CreditUsage.getUsageBySchool(school_id, options);
    const totalCount = await CreditUsage.countDocuments({ school_id });

    res.status(200).json({
      usage_history: usageHistory,
      pagination: {
        total: totalCount,
        limit: parseInt(limit),
        skip: parseInt(skip),
        has_more: (parseInt(skip) + parseInt(limit)) < totalCount
      },
      message: 'Credit usage history retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching credit usage history:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Obtenir tous les plans de souscription disponibles
const getAvailablePlans = async (req, res) => {
  try {
    const plans = await SubscriptionPlan.getActivePlans();

    res.status(200).json({
      plans,
      message: 'Available subscription plans retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching available plans:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Fonction pour récupérer les données d'utilisation des crédits pour les graphiques
const getCreditUsageAnalytics = async (req, res) => {
  try {
    const { school_id } = req.params;
    const { period = 'month' } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Calculer la période de temps
    const now = new Date();
    let startDate = new Date();
    let dateFormat = '%Y-%m-%d';

    switch (period) {
      case 'week':
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate.setDate(now.getDate() - 30);
        break;
      case 'quarter':
        startDate.setDate(now.getDate() - 90);
        dateFormat = '%Y-%U';
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        dateFormat = '%Y-%m';
        break;
    }

    // Récupérer les données d'utilisation des crédits
    const subscription = await SchoolSubscription.findOne({ school_id });
    if (!subscription) {
      return res.status(404).json({ message: 'School subscription not found' });
    }

    // Utiliser CreditPurchase pour récupérer les données réelles
    const CreditPurchase = require('../models/CreditPurchase');

    // Agrégation des achats de crédits
    const purchaseData = await CreditPurchase.aggregate([
      {
        $match: {
          school_id: ObjectId.createFromHexString(school_id),
          purchase_date: { $gte: startDate },
          payment_status: 'completed'
        }
      },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: dateFormat, date: '$purchase_date' } }
          },
          credits_purchased: { $sum: '$credits_purchased' },
          total_amount: { $sum: '$total_amount' },
          transaction_count: { $sum: 1 }
        }
      },
      { $sort: { '_id.date': 1 } }
    ]);

    // Récupérer l'utilisation des crédits via les étudiants enregistrés (Credit model)
    const Credit = require('../models/Credit');
    const usageData = await Credit.aggregate([
      {
        $match: {
          school_id: ObjectId.createFromHexString(school_id),
          paidAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: dateFormat, date: '$paidAt' } }
          },
          students_registered: { $sum: 1 },
          credits_used: { $sum: 1 }, // 1 crédit par étudiant
          total_revenue: { $sum: '$amountPaid' }
        }
      },
      { $sort: { '_id.date': 1 } }
    ]);

    // Combiner les données
    const dateMap = new Map();

    // Ajouter les achats de crédits
    purchaseData.forEach(item => {
      const date = item._id.date;
      dateMap.set(date, {
        date,
        credits_purchased: item.credits_purchased,
        credits_used: 0,
        students_registered: 0,
        credits_balance: subscription.credits_balance,
        total_amount: item.total_amount
      });
    });

    // Ajouter les utilisations (étudiants enregistrés)
    usageData.forEach(item => {
      const date = item._id.date;
      if (dateMap.has(date)) {
        dateMap.get(date).credits_used = item.credits_used;
        dateMap.get(date).students_registered = item.students_registered;
      } else {
        dateMap.set(date, {
          date,
          credits_purchased: 0,
          credits_used: item.credits_used,
          students_registered: item.students_registered,
          credits_balance: subscription.credits_balance,
          total_amount: 0
        });
      }
    });

    // Convertir en tableau et trier
    const analyticsData = Array.from(dateMap.values()).sort((a, b) =>
      new Date(a.date).getTime() - new Date(b.date).getTime()
    );

    res.status(200).json({
      school_id,
      period,
      data: analyticsData,
      summary: {
        total_purchased: purchaseData.reduce((sum, item) => sum + item.credits_purchased, 0),
        total_used: usageData.reduce((sum, item) => sum + item.credits_used, 0),
        current_balance: subscription.credits_balance,
        total_students: usageData.reduce((sum, item) => sum + item.students_registered, 0),
        total_revenue: purchaseData.reduce((sum, item) => sum + (item.total_amount || 0), 0)
      }
    });

  } catch (error) {
    console.error('Error fetching credit usage analytics:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error.message
    });
  }
};

// Fonction pour récupérer les données de distribution des souscriptions
const getSubscriptionDistribution = async (req, res) => {
  try {
    const { school_id } = req.params;

    // Si school_id est fourni, filtrer pour cette école spécifique
    // Sinon, récupérer toutes les données (pour super-admin)
    let matchStage = {};
    if (school_id) {
      matchStage = { school_id: ObjectId.createFromHexString(school_id) };
    }

    // Récupérer la distribution des plans
    const distribution = await SchoolSubscription.aggregate([
      ...(Object.keys(matchStage).length > 0 ? [{ $match: matchStage }] : []),
      {
        $group: {
          _id: '$plan_type',
          count: { $sum: 1 },
          total_revenue: { $sum: '$total_paid' },
          avg_credits: { $avg: '$credits_purchased' }
        }
      },
      {
        $project: {
          plan_type: '$_id',
          count: 1,
          revenue: '$total_revenue',
          avg_credits: { $round: ['$avg_credits', 0] },
          _id: 0
        }
      }
    ]);

    // Ajouter des couleurs pour les graphiques
    const colorMap = {
      'basic': '#3B82F6',
      'premium': '#10B981',
      'enterprise': '#F59E0B',
      'trial': '#6B7280',
      'custom': '#8B5CF6',
      'standard': '#F97316',
    };

    const distributionWithColors = distribution.map(item => ({
      ...item,
      color: colorMap[item.plan_type.toLowerCase()] || '#6B7280'
    }));

    // Récupérer les tendances mensuelles
    const monthlyTrends = await SchoolSubscription.aggregate([
      {
        $match: {
          ...matchStage,
          createdAt: {
            $gte: new Date(new Date().getFullYear(), 0, 1) // Début de l'année
          }
        }
      },
      {
        $group: {
          _id: {
            month: { $month: '$createdAt' },
            plan_type: '$plan_type'
          },
          count: { $sum: 1 },
          revenue: { $sum: '$total_paid' }
        }
      },
      {
        $group: {
          _id: '$_id.month',
          plans: {
            $push: {
              plan_type: '$_id.plan_type',
              count: '$count',
              revenue: '$revenue'
            }
          },
          total_revenue: { $sum: '$revenue' }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    // Formater les données mensuelles
    const monthNames = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun',
                       'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'];

    const formattedMonthly = monthlyTrends.map(item => {
      const monthData = {
        month: monthNames[item._id - 1],
        basic: 0,
        premium: 0,
        enterprise: 0,
        revenue: item.total_revenue
      };

      item.plans.forEach(plan => {
        monthData[plan.plan_type.toLowerCase()] = plan.count;
      });

      return monthData;
    });

    res.status(200).json({
      distribution: distributionWithColors,
      monthly_trends: formattedMonthly,
      summary: {
        total_subscriptions: distribution.reduce((sum, item) => sum + item.count, 0),
        total_revenue: distribution.reduce((sum, item) => sum + item.revenue, 0),
        most_popular_plan: distribution.reduce((prev, current) =>
          (prev.count > current.count) ? prev : current
        )?.plan_type || 'N/A'
      }
    });

  } catch (error) {
    console.error('Error fetching subscription distribution:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error.message
    });
  }
};

// Fonction pour récupérer les données complètes d'une école avec analyses
const getSchoolCompleteAnalytics = async (req, res) => {
  try {
    const { school_id } = req.params;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Récupérer les informations de l'école
    const school = await School.findById(school_id);
    if (!school) {
      return res.status(404).json({ message: 'School not found' });
    }

    // Récupérer la souscription de l'école
    const subscription = await SchoolSubscription.findOne({ school_id });
    if (!subscription) {
      return res.status(404).json({ message: 'School subscription not found' });
    }

    // Récupérer les achats de crédits
    const CreditPurchase = require('../models/CreditPurchase');
    const purchases = await CreditPurchase.find({
      school_id,
      payment_status: 'completed'
    }).sort({ purchase_date: -1 });

    // Calculer les statistiques
    const totalPurchased = purchases.reduce((sum, p) => sum + p.credits_purchased, 0);
    const totalRevenue = purchases.reduce((sum, p) => sum + p.total_amount, 0);
    const averagePurchase = purchases.length > 0 ? totalRevenue / purchases.length : 0;

    // Récupérer l'utilisation des crédits (étudiants enregistrés)
    const Credit = require('../models/Credit');
    const creditUsage = await Credit.find({ school_id });
    const totalUsed = creditUsage.length; // 1 crédit par étudiant

    // Calculer les jours restants estimés
    const currentBalance = subscription.credits_balance;
    const dailyUsage = totalUsed > 0 ? totalUsed / 30 : 0; // Approximation sur 30 jours
    const daysRemaining = dailyUsage > 0 ? Math.ceil(currentBalance / dailyUsage) : 0;

    // Calculer l'efficacité
    const efficiency = totalPurchased > 0 ? (totalUsed / totalPurchased) * 100 : 0;

    // ARPU (Average Revenue Per User)
    const arpu = totalUsed > 0 ? totalRevenue / totalUsed : 0;

    // Récupérer les transactions récentes
    const recentPurchases = purchases.slice(0, 10).map(p => ({
      purchase_id: p.purchase_id,
      credits_purchased: p.credits_purchased,
      total_amount: p.total_amount,
      purchase_date: p.purchase_date,
      payment_method: p.payment_method
    }));

    res.status(200).json({
      school: {
        _id: school._id,
        name: school.name,
        address: school.address,
        email: school.email,
        phone: school.phone
      },
      subscription: {
        plan_type: subscription.plan_type,
        status: subscription.status,
        credits_balance: subscription.credits_balance,
        credits_purchased: subscription.credits_purchased,
        credits_used: subscription.credits_used,
        subscription_start: subscription.subscription_start,
        subscription_end: subscription.subscription_end,
        features: subscription.features
      },
      analytics: {
        total_purchased: totalPurchased,
        total_used: totalUsed,
        total_revenue: totalRevenue,
        current_balance: currentBalance,
        efficiency: Math.round(efficiency),
        arpu: Math.round(arpu),
        average_purchase: Math.round(averagePurchase),
        days_remaining: daysRemaining,
        purchase_count: purchases.length
      },
      recent_purchases: recentPurchases
    });

  } catch (error) {
    console.error('Error fetching school complete analytics:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error.message
    });
  }
};

module.exports = {
  getSchoolSubscription,
  updateSchoolSubscription,
  getSubscriptionStats,
  checkCreditsAvailability,
  deductCredits,
  getCreditUsageHistory,
  getAvailablePlans,
  getCreditUsageAnalytics,
  getSubscriptionDistribution,
  getSchoolCompleteAnalytics
};
